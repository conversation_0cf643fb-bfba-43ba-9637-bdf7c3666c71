import React from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AuthProvider } from "@/contexts/AuthContext";
import { CompanyProvider } from "@/contexts/CompanyContext";

import Login from "./pages/Login";
import MainLayout from "./components/layout/MainLayout";
import AuthGuard from "./components/auth/AuthGuard";

import CaseDetail from "./pages/CaseDetail";
import NotFound from "./pages/NotFound";
import LeadsPage from "./pages/LeadsPage";
import WhatsAppPage from "./pages/WhatsAppPage";
import MarketingAutomationPage from "./pages/MarketingAutomationPage";
import CaseIntakePage from "./pages/CaseIntakePage";
import CaseOpenPage from "./pages/CaseOpenPage";
import CaseClosedPage from "./pages/CaseClosedPage";
import CasesPage from "./pages/CasesPage";


import DashboardPage from "./pages/DashboardPage";
import SuperAdminPage from "./pages/SuperAdminPage";
import CompanySettingsPage from "./pages/CompanySettingsPage";
import { VoiceTestPage } from "./pages/VoiceTestPage";
import BackgroundSyncService from "@/services/BackgroundSyncService";

// Optimized React Query configuration for performance
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 10 * 60 * 1000, // 10 minutes - longer stale time for better performance
      gcTime: 15 * 60 * 1000, // 15 minutes - longer garbage collection time
      retry: 2, // Reduce retry attempts
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000), // Faster backoff
      refetchOnWindowFocus: false, // Don't refetch on window focus
      refetchOnMount: false, // Don't refetch on mount if data exists
      placeholderData: (previousData: any) => previousData, // Show old data while fetching new
    },
    mutations: {
      retry: 1, // Reduce mutation retries
      retryDelay: attemptIndex => Math.min(500 * 2 ** attemptIndex, 5000), // Faster mutation backoff
    },
  },
});

const ProtectedWrapper = ({ children }: { children: React.ReactNode }) => (
  <AuthGuard>
    <CompanyProvider>
      <SidebarProvider>
        {children}
      </SidebarProvider>
    </CompanyProvider>
  </AuthGuard>
);

const App = () => {
  // Initialize background sync service for performance optimization
  React.useEffect(() => {
    const syncService = BackgroundSyncService.getInstance();
    syncService.start();

    return () => {
      syncService.stop();
    };
  }, []);

  try {
    return (
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <AuthProvider>
            <BrowserRouter>
              <div className="min-h-screen w-full hebrew-text">
                <Routes>
                  <Route path="/login" element={<Login />} />
                  <Route path="/" element={
                    <ProtectedWrapper>
                      <MainLayout />
                    </ProtectedWrapper>
                  }>
                    <Route index element={<DashboardPage />} />
                    <Route path="dashboard" element={<DashboardPage />} />
                    <Route path="office/leads" element={<LeadsPage />} />
                    <Route path="office/whatsapp" element={<WhatsAppPage />} />
                    <Route path="office/marketing-automation" element={<MarketingAutomationPage />} />

                    <Route path="cases" element={<CasesPage />} />
                    <Route path="cases/intake" element={<CaseIntakePage />} />
                    <Route path="cases/open" element={<CaseOpenPage />} />
                    <Route path="cases/closed" element={<CaseClosedPage />} />
                    <Route path="case/:id" element={<CaseDetail />} />

                    <Route path="super-admin" element={<SuperAdminPage />} />
                    <Route path="company-settings" element={<CompanySettingsPage />} />
                    <Route path="voice-test" element={<VoiceTestPage />} />
                  </Route>
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </div>
            </BrowserRouter>
          </AuthProvider>
        </TooltipProvider>
      </QueryClientProvider>
    );
  } catch (error) {
    console.error('App rendering error:', error);
    return (
      <div style={{ padding: '20px', background: 'red', color: 'white' }}>
        <h1>Error in App</h1>
        <p>Check console for details</p>
      </div>
    );
  }
};

export default App;