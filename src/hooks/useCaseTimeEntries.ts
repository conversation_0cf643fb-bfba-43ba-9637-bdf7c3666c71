import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { useToast } from './use-toast';

export interface CaseTimeEntry {
  id: string;
  case_id: string;
  task_id?: string;
  user_id: string;
  company_id: string;
  description?: string;
  start_time?: string;
  end_time?: string;
  duration: number; // in minutes
  total_cost?: number; // Calculated proportionally from case value
  created_at: string;
}

export const useCaseTimeEntries = (caseId?: string) => {
  const [timeEntries, setTimeEntries] = useState<CaseTimeEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTimer, setActiveTimer] = useState<{
    id: string;
    startTime: Date;
    description?: string;
  } | null>(null);
  const { user, userRole } = useAuth();
  const { toast } = useToast();

  const fetchTimeEntries = async () => {
    if (!user || !caseId) return;

    try {
      const { data, error } = await supabase
        .from('case_time_entries')
        .select('*')
        .eq('case_id', caseId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setTimeEntries(data || []);
    } catch (error) {
      console.error('Error fetching time entries:', error);
      toast({
        title: "שגיאה",
        description: "שגיאה בטעינת רישומי הזמן",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const startTimer = (description?: string) => {
    if (activeTimer) {
      stopTimer();
    }
    
    setActiveTimer({
      id: `temp-${Date.now()}`,
      startTime: new Date(),
      description,
    });
  };

  const stopTimer = async () => {
    if (!activeTimer || !caseId || !user || !userRole?.company_id) return;

    const endTime = new Date();
    const duration = Math.floor((endTime.getTime() - activeTimer.startTime.getTime()) / 1000 / 60); // minutes

    try {
      const { data, error } = await supabase
        .from('case_time_entries')
        .insert({
          case_id: caseId,
          user_id: user.id,
          company_id: userRole.company_id,
          description: activeTimer.description,
          start_time: activeTimer.startTime.toISOString(),
          end_time: endTime.toISOString(),
          duration,
        })
        .select()
        .single();

      if (error) throw error;

      setTimeEntries(prev => [data, ...prev]);
      setActiveTimer(null);
      
      toast({
        title: "הצלחה",
        description: `נרשמו ${duration} דקות`,
      });

      return data;
    } catch (error) {
      console.error('Error saving time entry:', error);
      toast({
        title: "שגיאה",
        description: "שגיאה בשמירת רישום הזמן",
        variant: "destructive",
      });
      setActiveTimer(null);
    }
  };

  const addManualTimeEntry = async (entryData: {
    case_id: string;
    task_id?: string;
    description?: string;
    start_time?: string;
    end_time?: string;
    duration: number;
  }) => {
    if (!user || !userRole?.company_id) return;

    try {
      const { data, error } = await supabase
        .from('case_time_entries')
        .insert({
          ...entryData,
          user_id: user.id,
          company_id: userRole.company_id,
        })
        .select()
        .single();

      if (error) throw error;

      setTimeEntries(prev => [data, ...prev]);
      toast({
        title: "הצלחה",
        description: "רישום הזמן נוסף בהצלחה",
      });

      return data;
    } catch (error) {
      console.error('Error creating time entry:', error);
      toast({
        title: "שגיאה",
        description: "שגיאה ביצירת רישום הזמן",
        variant: "destructive",
      });
    }
  };

  const updateTimeEntry = async (entryId: string, updates: Partial<CaseTimeEntry>) => {
    try {
      const { data, error } = await supabase
        .from('case_time_entries')
        .update(updates)
        .eq('id', entryId)
        .select()
        .single();

      if (error) throw error;

      setTimeEntries(prev => prev.map(e => e.id === entryId ? data : e));
      toast({
        title: "הצלחה",
        description: "רישום הזמן עודכן בהצלחה",
      });

      return data;
    } catch (error) {
      console.error('Error updating time entry:', error);
      toast({
        title: "שגיאה",
        description: "שגיאה בעדכון רישום הזמן",
        variant: "destructive",
      });
    }
  };

  const deleteTimeEntry = async (entryId: string) => {
    try {
      const { error } = await supabase
        .from('case_time_entries')
        .delete()
        .eq('id', entryId);

      if (error) throw error;

      setTimeEntries(prev => prev.filter(e => e.id !== entryId));
      toast({
        title: "הצלחה",
        description: "רישום הזמן נמחק בהצלחה",
      });
    } catch (error) {
      console.error('Error deleting time entry:', error);
      toast({
        title: "שגיאה",
        description: "שגיאה במחיקת רישום הזמן",
        variant: "destructive",
      });
    }
  };

  const getTotalTime = () => {
    return timeEntries.reduce((total, entry) => total + entry.duration, 0);
  };



  useEffect(() => {
    if (user && caseId) {
      fetchTimeEntries();
    }
  }, [user, caseId]);

  return {
    timeEntries,
    isLoading,
    activeTimer,
    startTimer,
    stopTimer,
    addManualTimeEntry,
    updateTimeEntry,
    deleteTimeEntry,
    getTotalTime,
    refetchTimeEntries: fetchTimeEntries,
  };
};