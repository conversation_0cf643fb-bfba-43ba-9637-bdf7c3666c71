import { Settings } from "lucide-react";
import { UserManagement } from "@/components/settings/UserManagement";

const SettingsPage = () => {

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-foreground mb-2 flex items-center gap-2" dir="rtl">
          <Settings className="h-8 w-8" />
          הגדרות
        </h1>
        <p className="text-muted-foreground" dir="rtl">ניהול הגדרות אישיות ומשתמשים</p>
      </div>

      {/* User Management Section */}
      <UserManagement />
    </div>
  );
};

export default SettingsPage;