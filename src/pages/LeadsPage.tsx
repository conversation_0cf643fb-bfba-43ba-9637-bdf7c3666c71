import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, Filter, FolderOpen } from "lucide-react";
import { LeadModal } from "@/components/office/LeadModal";
import { LeadsList } from "@/components/leads/LeadsList";
import { ActiveCallBar } from "@/components/leads/ActiveCallBar";
import { useLeads } from "@/hooks/useLeads";
import { useCallManager } from "@/hooks/useCallManager";
import { Lead } from "@/components/leads/LeadCard";

export default function LeadsPage() {
  const navigate = useNavigate();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingLead, setEditingLead] = useState<Lead | null>(null);
  const [statusFilter, setStatusFilter] = useState("הכל");
  
  const { leads, isLoading, addLead, updateLead, deleteLead, refetchLeads } = useLeads();
  const {
    activeCall,
    isLoading: isCallLoading,
    initiateCall,
    hangupCall,
    muteCall,
    unmuteCall,
    acceptCall,
    rejectCall,
    isMuted,
    device,
    voiceCall
  } = useCallManager();

  const filteredLeads = statusFilter === "הכל" 
    ? leads 
    : leads.filter(lead => lead.status === statusFilter);

  const statusOptions = [
    "הכל",
    "ליד חדש",
    "צריך פולואפ", 
    "לקוח סגור",
    "לא ענה",
    "לא מעוניין",
    "לא מתאים"
  ];

  const handleEdit = (lead: Lead) => {
    setEditingLead(lead);
    setIsModalOpen(true);
  };

  const handleAddLead = async (leadData: Omit<Lead, 'id' | 'created_at' | 'updated_at'>) => {
    return await addLead(leadData);
  };

  const handleUpdateLead = async (leadId: string, updates: Partial<Lead>) => {
    return await updateLead(leadId, updates);
  };

  const handleWhatsApp = (phoneNumber: string) => {
    // Find the lead by phone number to get the lead ID
    const lead = leads.find(l => l.phone === phoneNumber);
    if (lead) {
      navigate(`/office/whatsapp?lead=${lead.id}`);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">טוען לידים...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {activeCall && (
        <ActiveCallBar
          activeCall={activeCall}
          onHangup={hangupCall}
          onMute={muteCall}
          onUnmute={unmuteCall}
          onAccept={acceptCall}
          onReject={rejectCall}
          isMuted={isMuted}
        />
      )}

      <div className={`flex items-center justify-between ${activeCall ? 'mt-20' : ''}`}>
        <div>
          <h1 className="text-2xl font-bold text-foreground">לידים</h1>
          <p className="text-muted-foreground">ניהול לידים ולקוחות פוטנציאליים ({filteredLeads.length} לידים)</p>
        </div>
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-primary" />
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map((status) => (
                  <SelectItem key={status} value={status}>
                    {status}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <Button 
            className="btn-professional flex items-center gap-2"
            onClick={() => setIsModalOpen(true)}
          >
            <Plus className="w-4 h-4" />
            הוסף ליד חדש
          </Button>
        </div>
      </div>

      <LeadsList
        leads={filteredLeads}
        onCall={initiateCall}
        onEdit={handleEdit}
        onDelete={deleteLead}
        onWhatsApp={handleWhatsApp}
        isCallLoading={isCallLoading}
        onRefresh={refetchLeads}
      />

      <LeadModal 
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setEditingLead(null);
        }}
        editingLead={editingLead}
        onAddLead={handleAddLead}
        onUpdateLead={handleUpdateLead}
      />
    </div>
  );
}