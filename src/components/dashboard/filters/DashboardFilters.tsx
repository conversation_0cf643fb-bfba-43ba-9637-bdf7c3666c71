import React, { useState, useEffect, useCallback } from 'react';
import { DateRange } from 'react-day-picker';
import { subDays, startOfMonth, endOfMonth } from 'date-fns';
import { DateRangePicker } from './DateRangePicker';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Filter, RotateCcw } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import type { DashboardFilters } from '@/hooks/useDashboardData';

interface DashboardFiltersProps {
  filters: DashboardFilters;
  onFiltersChange: (filters: DashboardFilters) => void;
}

interface CaseType {
  id: string;
  name: string;
}

export const DashboardFilters = ({ filters, onFiltersChange }: DashboardFiltersProps) => {
  const [caseTypes, setCaseTypes] = useState<CaseType[]>([]);
  const { userRole } = useAuth();

  // Fetch case types
  useEffect(() => {
    const fetchCaseTypes = async () => {
      if (!userRole?.company_id) return;

      const { data, error } = await supabase
        .from('case_types')
        .select('id, name')
        .eq('company_id', userRole.company_id);

      if (!error && data) {
        setCaseTypes(data);
      }
    };

    fetchCaseTypes();
  }, [userRole]);

  const handleDateRangeChange = useCallback((range: DateRange | undefined) => {
    if (range?.from && range?.to) {
      onFiltersChange({
        ...filters,
        dateRange: {
          from: range.from,
          to: range.to
        }
      });
    }
  }, [filters, onFiltersChange]);

  const handleCaseTypeChange = useCallback((value: string) => {
    onFiltersChange({
      ...filters,
      caseTypeId: value === 'all' ? undefined : value
    });
  }, [filters, onFiltersChange]);

  const resetFilters = useCallback(() => {
    const today = new Date();
    const oneYearAgo = subDays(today, 365); // Changed from 30 to 365 days

    onFiltersChange({
      dateRange: {
        from: oneYearAgo,
        to: today
      },
      caseTypeId: undefined
    });
  }, [onFiltersChange]);

  const setPresetRange = (preset: 'today' | 'week' | 'month' | 'quarter' | 'year') => {
    const today = new Date();
    let from: Date;
    let to: Date = today;

    switch (preset) {
      case 'today':
        from = today;
        break;
      case 'week':
        from = subDays(today, 7);
        break;
      case 'month':
        from = startOfMonth(today);
        to = endOfMonth(today);
        break;
      case 'quarter':
        from = subDays(today, 90);
        break;
      case 'year':
        from = subDays(today, 365);
        break;
      default:
        from = subDays(today, 30);
    }

    onFiltersChange({
      ...filters,
      dateRange: { from, to }
    });
  };

  return (
    <Card className="mb-6">
      <CardContent className="p-6">
        <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-muted-foreground" />
            <span className="font-medium text-foreground">מסננים:</span>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            {/* Date Range Picker */}
            <div className="flex flex-col gap-2">
              <label className="text-sm font-medium text-foreground">טווח תאריכים</label>
              <DateRangePicker
                value={filters.dateRange}
                onChange={handleDateRangeChange}
              />
            </div>

            {/* Case Type Filter */}
            <div className="flex flex-col gap-2">
              <label className="text-sm font-medium text-foreground">סוג תיק</label>
              <Select
                value={filters.caseTypeId || 'all'}
                onValueChange={handleCaseTypeChange}
              >
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="בחר סוג תיק" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">כל סוגי התיקים</SelectItem>
                  {caseTypes.map((type) => (
                    <SelectItem key={type.id} value={type.id}>
                      {type.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-2">
            {/* Preset Buttons */}
            <div className="flex gap-2 flex-wrap">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPresetRange('today')}
                className="text-xs"
              >
                היום
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPresetRange('week')}
                className="text-xs"
              >
                שבוע
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPresetRange('month')}
                className="text-xs"
              >
                חודש
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPresetRange('quarter')}
                className="text-xs"
              >
                רבעון
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPresetRange('year')}
                className="text-xs"
              >
                שנה
              </Button>
            </div>

            {/* Reset Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={resetFilters}
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              איפוס
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
