@tailwind base;
@tailwind components;
@tailwind utilities;

/* Professional Legal Practice Management Design System */
/* All colors MUST be HSL for proper theming */

@layer base {
  :root {
    /* Professional Legal Theme - Light Mode - All Text Dark */
    --background: 248 250 252; /* slate-50 */
    --foreground: 15 23 42;    /* slate-800 - dark text */

    --card: 255 255 255;       /* white */
    --card-foreground: 15 23 42; /* slate-800 - dark text */

    --popover: 255 255 255;
    --popover-foreground: 15 23 42; /* dark text */

    /* Professional Blue Primary Palette */
    --primary: 220 38% 48%;     /* Professional blue */
    --primary-foreground: 255 255 255;
    --primary-light: 220 38% 65%;
    --primary-dark: 220 45% 35%;

    /* Sophisticated Secondary */
    --secondary: 220 38% 48%;   /* Same as primary blue */
    --secondary-foreground: 255 255 255; /* white text on blue */

    /* Neutral Grays - All Text Dark */
    --muted: 241 245 249;       /* slate-100 */
    --muted-foreground: 15 23 42; /* dark text like foreground */

    --accent: 236 253 245;      /* emerald-50 */
    --accent-foreground: 6 78 59; /* emerald-700 */

    /* Professional Status Colors */
    --success: 142 76% 36%;     /* emerald-600 */
    --success-foreground: 255 255 255;
    
    --warning: 43 96% 56%;      /* yellow-400 */
    --warning-foreground: 15 23 42;
    
    --destructive: 0 72% 51%;   /* red-500 */
    --destructive-foreground: 255 255 255;

    /* Borders & Inputs */
    --border: 226 232 240;      /* slate-200 */
    --input: 226 232 240;
    --ring: 220 38% 48%;        /* matches primary */

    --radius: 0.5rem;

    /* Sidebar Professional Theme - All Text Dark */
    --sidebar-background: 248 250 252; /* slate-50 */
    --sidebar-foreground: 15 23 42;    /* dark text */
    --sidebar-primary: 220 38% 48%;    /* Professional blue */
    --sidebar-primary-foreground: 255 255 255;
    --sidebar-accent: 241 245 249;     /* slate-100 */
    --sidebar-accent-foreground: 15 23 42; /* dark text */
    --sidebar-border: 226 232 240;     /* slate-200 */
    --sidebar-ring: 220 38% 48%;

    /* Professional Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary-light)) 100%);
    --gradient-hero: linear-gradient(135deg, hsl(var(--primary-dark)) 0%, hsl(var(--primary)) 50%, hsl(var(--primary-light)) 100%);
    --gradient-subtle: linear-gradient(180deg, hsl(var(--background)) 0%, hsl(var(--muted)) 100%);

    /* Professional Shadows */
    --shadow-sm: 0 1px 2px 0 hsl(var(--primary) / 0.05);
    --shadow-md: 0 4px 6px -1px hsl(var(--primary) / 0.1), 0 2px 4px -2px hsl(var(--primary) / 0.1);
    --shadow-lg: 0 10px 15px -3px hsl(var(--primary) / 0.1), 0 4px 6px -4px hsl(var(--primary) / 0.1);
    --shadow-xl: 0 20px 25px -5px hsl(var(--primary) / 0.1), 0 8px 10px -6px hsl(var(--primary) / 0.1);

    /* Professional Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  .dark {
    /* Dark Mode Professional Theme */
    --background: 15 23 42;      /* slate-800 */
    --foreground: 248 250 252;   /* slate-50 */

    --card: 30 41 59;           /* slate-700 */
    --card-foreground: 248 250 252;

    --popover: 30 41 59;
    --popover-foreground: 248 250 252;

    --primary: 220 38% 65%;     /* Lighter blue for dark mode */
    --primary-foreground: 15 23 42;

    --secondary: 51 65 85;      /* slate-400 */
    --secondary-foreground: 15 23 42;

    --muted: 51 65 85;          /* slate-600 */
    --muted-foreground: 226 232 240; /* slate-200 - better contrast for dark mode */

    --accent: 6 78 59;          /* emerald-700 */
    --accent-foreground: 236 253 245;

    --destructive: 0 62% 50%;
    --destructive-foreground: 248 250 252;

    --border: 51 65 85;         /* slate-600 */
    --input: 51 65 85;
    --ring: 220 38% 65%;

    --sidebar-background: 15 23 42;
    --sidebar-foreground: 226 232 240; /* slate-200 - better contrast for dark mode */
    --sidebar-primary: 220 38% 65%;
    --sidebar-primary-foreground: 15 23 42;
    --sidebar-accent: 51 65 85;
    --sidebar-accent-foreground: 248 250 252;
    --sidebar-border: 51 65 85;
    --sidebar-ring: 220 38% 65%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    direction: rtl;
  }

  body {
    @apply bg-background text-foreground font-assistant text-base;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-size: 16px; /* Slightly larger base font size */
  }

  /* Hebrew Typography Optimizations */
  .hebrew-text {
    font-family: 'Assistant', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
    font-feature-settings: "kern" 1;
    text-rendering: optimizeLegibility;
  }

  /* Slightly larger font sizes for better readability */
  p, span, div {
    font-size: 1rem; /* 16px */
  }

  .text-sm {
    font-size: 0.9rem !important; /* 14.4px instead of 14px */
  }

  .text-xs {
    font-size: 0.8rem !important; /* 12.8px instead of 12px */
  }

  /* Table text improvements */
  table {
    font-size: 1rem;
  }

  th, td {
    font-size: 0.95rem; /* Slightly larger than default */
    vertical-align: middle; /* Center align vertically */
  }

  /* Professional table styling */
  .professional-table th {
    text-align: center;
    font-weight: 600;
    padding: 12px 8px;
  }

  .professional-table td {
    text-align: center;
    padding: 12px 8px;
  }

  /* Professional Button Styles - Gradient Blue */
  .btn-professional {
    @apply bg-gradient-to-r from-primary to-primary-light text-primary-foreground;
    @apply shadow-md hover:shadow-lg transition-all duration-300;
    @apply border border-primary/20;
  }

  /* All Buttons Use Blue Gradient */
  .btn-primary {
    @apply bg-gradient-to-r from-primary to-primary-light text-primary-foreground;
    @apply hover:from-primary-dark hover:to-primary transition-all duration-300;
  }

  /* All Icons Use Primary Blue */
  .icon-primary {
    @apply text-primary;
  }

  /* Professional Card Styles */
  .card-professional {
    @apply bg-card border border-border shadow-sm;
    @apply hover:shadow-md transition-shadow duration-300;
  }

  /* Status Indicators */
  .status-success {
    @apply bg-success/10 text-success border border-success/20;
  }

  .status-warning {
    @apply bg-warning/10 text-warning-foreground border border-warning/30;
  }

  .status-pending {
    @apply bg-muted text-muted-foreground border border-border;
  }
}

/* RTL Specific Overrides */
.rtl-flip {
  transform: scaleX(-1);
}

/* Professional Data Table Styles */
.professional-table {
  @apply w-full border-collapse;
}

.professional-table th {
  @apply bg-muted/30 text-foreground font-semibold px-4 py-3 text-right border-b-2 border-primary/20;
}

.professional-table td {
  @apply px-4 py-3 text-right border-b border-border/50 text-foreground;
}

.professional-table tr:hover {
  @apply bg-primary/5;
}

/* Professional Form Styles */
.form-professional label {
  @apply text-sm font-semibold text-foreground mb-2 block;
}

.form-professional input,
.form-professional select,
.form-professional textarea {
  @apply w-full px-3 py-2 border border-input bg-background text-foreground font-medium;
  @apply focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary;
  @apply transition-colors duration-200;
}

/* WhatsApp Container Glow Effects */
.whatsapp-glow {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    0 0 0 1px hsl(var(--accent) / 0.2),
    0 0 20px hsl(var(--accent) / 0.15);
  border: 1px solid hsl(var(--accent) / 0.3);
  transition: all 0.3s ease;
}

.whatsapp-glow:hover {
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05),
    0 0 0 1px hsl(var(--accent) / 0.3),
    0 0 30px hsl(var(--accent) / 0.25);
}